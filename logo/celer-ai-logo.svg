<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for the main background -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient for the stethoscope -->
    <linearGradient id="stethoscopeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>
    
    <!-- Ping animation -->
    <animate id="pingAnimation" attributeName="r" values="0;20;0" dur="2s" repeatCount="indefinite" />
    <animate id="pingOpacity" attributeName="opacity" values="1;0;1" dur="2s" repeatCount="indefinite" />
  </defs>
  
  <!-- Main background with rounded corners -->
  <rect x="32" y="32" width="448" height="448" rx="80" ry="80" fill="url(#bgGradient)" />
  
  <!-- Shadow effect -->
  <rect x="36" y="36" width="448" height="448" rx="80" ry="80" fill="rgba(0,0,0,0.1)" />
  <rect x="32" y="32" width="448" height="448" rx="80" ry="80" fill="url(#bgGradient)" />
  
  <!-- Stethoscope design -->
  <g transform="translate(256,256)">
    <!-- Main tube/cord -->
    <path d="M-60,-80 Q-80,-60 -80,-20 Q-80,20 -60,40 L-40,60 Q-20,80 20,80 Q60,80 80,60 L100,40 Q120,20 120,-20 Q120,-60 100,-80" 
          stroke="url(#stethoscopeGradient)" stroke-width="12" fill="none" stroke-linecap="round"/>
    
    <!-- Left earpiece -->
    <circle cx="-60" cy="-80" r="8" fill="url(#stethoscopeGradient)"/>
    <path d="M-68,-80 Q-80,-68 -80,-50" stroke="url(#stethoscopeGradient)" stroke-width="6" fill="none" stroke-linecap="round"/>
    
    <!-- Right earpiece -->
    <circle cx="60" cy="-80" r="8" fill="url(#stethoscopeGradient)"/>
    <path d="M68,-80 Q80,-68 80,-50" stroke="url(#stethoscopeGradient)" stroke-width="6" fill="none" stroke-linecap="round"/>
    
    <!-- Chest piece (main circular part) -->
    <circle cx="0" cy="60" r="35" fill="url(#stethoscopeGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <circle cx="0" cy="60" r="25" fill="rgba(255,255,255,0.2)"/>
    <circle cx="0" cy="60" r="15" fill="rgba(255,255,255,0.3)"/>
    
    <!-- Connection tube to chest piece -->
    <path d="M0,25 Q0,40 0,60" stroke="url(#stethoscopeGradient)" stroke-width="8" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- Pinging dot (top-right) -->
  <g transform="translate(400,112)">
    <!-- Static orange dot -->
    <circle cx="0" cy="0" r="16" fill="#fb923c"/>
    
    <!-- Animated ping rings -->
    <circle cx="0" cy="0" r="16" fill="#fb923c" opacity="0.7">
      <animate attributeName="r" values="16;32;16" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="0" cy="0" r="16" fill="#fb923c" opacity="0.5">
      <animate attributeName="r" values="16;40;16" dur="2s" repeatCount="indefinite" begin="0.5s"/>
      <animate attributeName="opacity" values="0.5;0;0.5" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
  </g>
  
  <!-- Optional: Subtle highlight on the main background -->
  <rect x="32" y="32" width="448" height="224" rx="80" ry="40" fill="url(#bgGradient)" opacity="0.1"/>
</svg>
