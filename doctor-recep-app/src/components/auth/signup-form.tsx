'use client'

import { useActionState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { signup } from '@/lib/actions/auth'
import { FormState } from '@/lib/types'

// CORRECTED initialState to conform to FormState interface
const initialState: FormState = {
  success: false,
  message: '',
  // fieldErrors is optional, so it can be omitted or set to an empty object if needed
  // fieldErrors: {},
}

interface SignupFormProps {
  referralCode?: string
}

export function SignupForm({ referralCode }: SignupFormProps) {
  const [state, formAction, isPending] = useActionState(signup, initialState)
  const router = useRouter()

  // Auto-redirect to login page after 3 seconds for successful account creation
  useEffect(() => {
    if (state?.success && state?.message?.includes('Account created successfully')) {
      const timer = setTimeout(() => {
        router.push('/login')
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [state?.success, state?.message, router])

  return (
    <form action={formAction} className="mt-8 space-y-6">
      {/* Hidden field for referral code */}
      {referralCode && (
        <input
          type="hidden"
          name="referral_code"
          value={referralCode}
        />
      )}
      
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Full Name
          </label>
          <input
            id="name"
            name="name"
            type="text"
            autoComplete="name"
            required
            className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Dr. John Doe"
          />
          {/* Use state.fieldErrors consistent with FormState definition */}
          {state?.fieldErrors?.name && (
            <p className="mt-1 text-sm text-red-600">{state.fieldErrors.name[0]}</p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email Address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="<EMAIL>"
          />
          {/* Use state.fieldErrors consistent with FormState definition */}
          {state?.fieldErrors?.email && (
            <p className="mt-1 text-sm text-red-600">{state.fieldErrors.email[0]}</p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            required
            className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Password"
          />
          {/* Use state.fieldErrors consistent with FormState definition */}
          {state?.fieldErrors?.password && (
            <p className="mt-1 text-sm text-red-600">{state.fieldErrors.password[0]}</p>
          )}
        </div>

        <div>
          <label htmlFor="clinic_name" className="block text-sm font-medium text-gray-700">
            Hospital Name
          </label>
          <input
            id="clinic_name"
            name="clinic_name"
            type="text"
            className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="ABC Medical Center"
          />
          {/* Use state.fieldErrors consistent with FormState definition */}
          {state?.fieldErrors?.clinic_name && (
            <p className="mt-1 text-sm text-red-600">{state.fieldErrors.clinic_name[0]}</p>
          )}
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
            Phone Number
          </label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 flex items-center">
              <span className="text-gray-500 sm:text-sm px-3 py-2 border-r border-gray-300 bg-gray-50 rounded-l-md">+91</span>
            </div>
            <input
              id="phone"
              name="phone"
              type="tel"
              autoComplete="tel"
              className="pl-14 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm w-full"
              placeholder="9876543210"
            />
          </div>
          {/* Use state.fieldErrors consistent with FormState definition */}
          {state?.fieldErrors?.phone && (
            <p className="mt-1 text-sm text-red-600">{state.fieldErrors.phone[0]}</p>
          )}
        </div>

        <div>
          <label htmlFor="referral_code" className="block text-sm font-medium text-gray-700">
            Referral ID (Optional)
          </label>
          <input
            id="referral_id"
            name="referral_code"
            type="text"
            className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter referral ID if you have one (optional)"
            defaultValue={referralCode || ''}
          />
          {/* Use state.fieldErrors consistent with FormState definition */}
          {state?.fieldErrors?.referral_code && (
            <p className="mt-1 text-sm text-red-600">{state.fieldErrors.referral_id[0]}</p>
          )}
        </div>
      </div>

      {state?.message && (
        <div className={`rounded-md p-4 ${
          state.success
            ? 'bg-green-50 border border-green-200'
            : 'bg-red-50 border border-red-200'
        }`}>
          <p className={`text-sm ${
            state.success ? 'text-green-800' : 'text-red-800'
          }`}>
            {state.message}
            {state.success && state.message?.includes('Account created successfully') && (
              <span className="block mt-2 text-green-600 text-xs">
                Redirecting to login page in 3 seconds...
              </span>
            )}
          </p>
        </div>
      )}

      <div>
        <button
          type="submit"
          disabled={isPending}
          className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isPending ? 'Creating account...' : 'Create account'}
        </button>
      </div>
    </form>
  )
}